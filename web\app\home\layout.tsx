'use client'
import Sidebar from './Sidebar'
import { createContext, useContext, useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import useSWR from 'swr'
// import Avatar from '@/app/components/base/avatar'
import decorate from '@/assets/images/layout/decorate.png'
import CustomDialog from '@/app/components/base/dialog'
import LoginPage from '@/app/home/<USER>/page'
import { fetchUserProfile } from '@/service/common'
import type { UserProfileResponse } from '@/models/common'

// 声明window扩展属性用于全局登录弹窗
declare global {
  interface Window {
    showHomeLoginDialog?: () => void
  }
}

// 创建 HomeContext
type HomeContextType = {
  isLogin: boolean
  showLoginDialog: () => void
}

const HomeContext = createContext<HomeContextType | null>(null)

export const useHomeContext = () => {
  const context = useContext(HomeContext)
  if (!context)
    throw new Error('无用户信息')
  return context
}

function LayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // 使用fetchUserProfile接口获取用户信息
  const { data: userProfileResponse } = useSWR(
    { url: '/account/profile', params: {} },
    fetchUserProfile,
  )

  // 处理用户信息
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null)

  useEffect(() => {
    if (userProfileResponse) {
      userProfileResponse.json().then((profile: UserProfileResponse) => {
        setUserProfile(profile)
      }).catch(() => {
        setUserProfile(null)
      })
    }
  }, [userProfileResponse])

  const isLogin = !!userProfile?.id
  const [selectedMenu, setSelectedMenu] = useState(localStorage.getItem('selectedMenu') || 'HomePage')
  const [showLoginDialog, setShowLoginDialog] = useState(false)

  const showLoginDialogHandler = () => setShowLoginDialog(true)

  // 全局挂载弹窗触发方法
  useEffect(() => {
    window.showHomeLoginDialog = showLoginDialogHandler
    return () => {
      delete window.showHomeLoginDialog
    }
  }, [])

  const homeContextValue = {
    isLogin,
    showLoginDialog: showLoginDialogHandler,
  }

  return (
    <HomeContext.Provider value={homeContextValue}>
      <div className="flex min-h-screen w-full bg-[#F3F4F6] font-[PingFangSC] ">
        {/* 侧边栏 */}
        <Sidebar onSelect={setSelectedMenu} selectedKey={selectedMenu} />
        {/* 主体内容 */}
        <main className="relative flex h-screen flex-1 flex-col items-center overflow-auto rounded-[11px] border-[1px] border-[#FFFFFF]" style={{ background: 'linear-gradient(180deg, #ECF4FF 0%, #FFFFFF 30%, #FFFFFF 100%)' }}>
          <img src={decorate.src} alt="decorateIcon" className='absolute right-[285px] top-[35px] ' />
          {children}
          {/* 全局登录弹窗 */}
          <CustomDialog
            show={showLoginDialog}
            onClose={() => setShowLoginDialog(false)}
            className="relative p-0"
            maskClassName="!bg-black !bg-opacity-60"
          >
            <LoginPage
              onSuccess={() => {
                // 登录成功后刷新页面
                window.location.reload()
              }}
            />
            <button
              className="absolute right-[20px] top-[15px] size-[20px] text-[20px] text-[#212121]"
              onClick={() => setShowLoginDialog(false)}
            >
              ×
            </button>
          </CustomDialog>
        </main>
      </div>
    </HomeContext.Provider>
  )
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <LayoutContent>{children}</LayoutContent>
  )
}
